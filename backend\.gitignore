# ===== NODE.JS =====
# Dependency directories
node_modules/
jspm_packages/

# ===== ENVIRONMENT VARIABLES (CRITICAL FOR SECURITY) =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*
!.env.example

# ===== LOGS =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===== RUNTIME DATA =====
pids
*.pid
*.seed
*.pid.lock

# ===== COVERAGE AND TESTING =====
coverage/
*.lcov
.nyc_output/
test-results/
junit.xml

# ===== CACHE DIRECTORIES =====
.npm
.eslintcache
.stylelintcache
.cache
.parcel-cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===== BUILD OUTPUTS =====
build/
dist/
out/
.next
.nuxt

# ===== DATABASE FILES =====
*.db
*.sqlite
*.sqlite3
.dynamodb/

# ===== UPLOADS AND TEMPORARY FILES =====
uploads/
temp/
tmp/
*.tmp
*.tgz

# ===== OS GENERATED FILES =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== IDE AND EDITOR FILES =====
.vscode/
.idea/
*.swp
*.swo
*~

# ===== SSL CERTIFICATES AND KEYS =====
*.pem
*.key
*.crt
*.cert

# ===== BACKUP FILES =====
*.backup
*.bak
*.old

# ===== PACKAGE MANAGER =====
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===== MISCELLANEOUS =====
.sass-cache/
connect.lock
typings/
.tern-port
.node_repl_history