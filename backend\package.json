{"name": "backend", "version": "1.0.0", "main": "src/index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "seed": "node src/scripts/seedData.js", "seed:dev": "nodemon src/scripts/seedData.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "multer": "^2.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "keywords": [], "author": "", "license": "ISC", "description": ""}